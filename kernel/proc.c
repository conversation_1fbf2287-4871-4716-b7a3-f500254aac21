#include "../include/types.h"
#include "../include/defs.h"
#include "../include/proc.h"
#include "../include/memlayout.h"
#include "../include/x86.h"
#include "../include/mmu.h"

// 全局进程表
struct proc procs[MAXPROGS];
struct proc *current_proc = 0;
static uint next_pid = 1;

// 初始化进程管理
void proc_init(void)
{
    int i;
    for (i = 0; i < MAXPROGS; i++)
    {
        procs[i].state = UNUSED;
        procs[i].pid = 0;
    }
    cprintf("Process management initialized\n");
}

// 分配一个新进程
struct proc *proc_alloc(void)
{
    int i;
    for (i = 0; i < MAXPROGS; i++)
    {
        if (procs[i].state == UNUSED)
        {
            procs[i].state = EMBRYO;
            procs[i].pid = next_pid++;
            procs[i].stack_base = USTACKBASE;
            procs[i].stack_size = USTACKSIZE;
            return &procs[i];
        }
    }
    return 0; // 没有可用的进程槽
}

// 释放进程
void proc_free(struct proc *p)
{
    if (p == 0)
        return;

    p->state = UNUSED;
    p->pid = 0;
    p->entry = 0;
    p->size = 0;
}

// 加载程序到进程（使用ELF加载器）
int proc_load(struct proc *p, void *binary, uint size)
{
    if (p == 0 || binary == 0 || size == 0)
        return -1;

    if (size > PROGSIZE)
        return -1; // 程序太大

    // 使用ELF加载器
    if (elf_load(p, binary, size) < 0)
    {
        cprintf("Failed to load ELF binary\n");
        return -1;
    }

    // 初始化用户栈
    p->context.esp = p->stack_base + p->stack_size - 4; // 栈顶
    p->context.eip = p->entry;                          // 程序入口
    p->context.eflags = 0x202;                          // 启用中断

    cprintf("Process %d loaded at 0x%x, size %d bytes\n", p->pid, p->entry, size);
    return 0;
}

// 运行进程（简化版，暂时不使用特权级切换）
void proc_run(struct proc *p)
{
    if (p == 0 || p->state != RUNNABLE)
        return;

    current_proc = p;
    p->state = RUNNING;

    cprintf("Running process %d (%s) at 0x%x\n", p->pid, p->name, p->entry);

    // 简化的进程执行：直接调用用户程序函数
    // 注意：这是一个简化的实现，没有真正的用户态/内核态隔离
    void (*user_func)(void) = (void (*)(void))p->entry;

    // 执行用户程序
    if (user_func)
    {
        // 在执行前清理当前进程状态，防止双重故障
        cprintf("Executing user program...\n");
        user_func();
        cprintf("User program returned\n");
    }

    // 程序执行完毕，如果没有显式退出
    if (current_proc == p)
    {
        cprintf("Process %d completed without explicit exit\n", p->pid);
        current_proc->state = ZOMBIE;
        current_proc = 0;
    }
}

// 进程退出
void proc_exit(int status)
{
    if (current_proc == 0)
        return;

    cprintf("Process %d (%s) exited with status %d\n",
            current_proc->pid, current_proc->name, status);

    current_proc->state = ZOMBIE;

    // 清理当前进程指针
    struct proc *exiting_proc = current_proc;
    current_proc = 0;

    // 标记进程为已完成
    exiting_proc->state = ZOMBIE;

    // 注意：在真实系统中，这里应该进行上下文切换回到调度器
    // 但在我们的简化实现中，我们直接返回到批处理系统
}
