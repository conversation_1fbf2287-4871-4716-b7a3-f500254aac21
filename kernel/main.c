#include "../include/types.h"
#include "../include/defs.h"
#include "../include/x86.h"
#include "../include/uart.h"
#include "../include/proc.h"
#include "../include/trap.h"
#include "../include/syscall.h"
#include "../include/mmu.h"

// 全局描述符表
struct segdesc gdt[7];

// 任务状态段
struct tss tss;

// 内核栈
static char kstack[KSTACKSIZE];

// 设置段描述符
static void setgdt(struct segdesc *gdt, uint type, uint base, uint lim, uint dpl)
{
  gdt->lim_15_0 = lim;
  gdt->base_15_0 = base;
  gdt->base_23_16 = base >> 16;
  gdt->type = type;
  gdt->s = 1;
  gdt->dpl = dpl;
  gdt->p = 1;
  gdt->lim_19_16 = lim >> 16;
  gdt->avl = 0;
  gdt->rsv1 = 0;
  gdt->db = 1;
  gdt->g = 1;
  gdt->base_31_24 = base >> 24;
}

// 初始化GDT
void gdt_init(void)
{
  // 空描述符
  gdt[0] = (struct segdesc){0};

  // 内核代码段 (DPL=0)
  setgdt(&gdt[SEG_KCODE], STA_X | STA_R, 0, 0xffffffff, DPL_KERNEL);

  // 内核数据段 (DPL=0)
  setgdt(&gdt[SEG_KDATA], STA_W, 0, 0xffffffff, DPL_KERNEL);

  // 用户代码段 (DPL=3)
  setgdt(&gdt[SEG_UCODE], STA_X | STA_R, 0, 0xffffffff, DPL_USER);

  // 用户数据段 (DPL=3)
  setgdt(&gdt[SEG_UDATA], STA_W, 0, 0xffffffff, DPL_USER);

  // 初始化TSS
  tss.ss0 = SEG_KDATA << 3;
  tss.esp0 = (uint)kstack + KSTACKSIZE;
  tss.iomb = sizeof(tss);

  // TSS描述符 (DPL=0, 类型=0x9表示可用的32位TSS)
  gdt[SEG_TSS].lim_15_0 = sizeof(tss) - 1;
  gdt[SEG_TSS].base_15_0 = (uint)&tss;
  gdt[SEG_TSS].base_23_16 = (uint)&tss >> 16;
  gdt[SEG_TSS].type = 0x9; // 可用的32位TSS
  gdt[SEG_TSS].s = 0;      // 系统段
  gdt[SEG_TSS].dpl = DPL_KERNEL;
  gdt[SEG_TSS].p = 1;
  gdt[SEG_TSS].lim_19_16 = ((sizeof(tss) - 1) >> 16) & 0xF;
  gdt[SEG_TSS].avl = 0;
  gdt[SEG_TSS].rsv1 = 0;
  gdt[SEG_TSS].db = 1; // 32位TSS
  gdt[SEG_TSS].g = 0;  // 字节粒度
  gdt[SEG_TSS].base_31_24 = (uint)&tss >> 24;

  // 加载GDT
  struct
  {
    ushort limit;
    uint base;
  } __attribute__((packed)) gdtr;

  gdtr.limit = sizeof(gdt) - 1;
  gdtr.base = (uint)gdt;
  lgdt(&gdtr);

  // 加载TSS
  ltr(SEG_TSS << 3);

  cprintf("GDT and TSS initialized with user/kernel segments\n");
}

// 内核主函数
void kmain(void)
{
  // 初始化控制台
  consoleinit();

  // 输出启动信息
  cprintf("MyOS - Mini Operating System with Batch Processing\n");
  cprintf("================================================\n");

  // 初始化GDT
  gdt_init();

  // 初始化串行端口
  uartinit();

  // 初始化进程管理
  proc_init();

  // 初始化系统调用
  syscall_init();

  // 初始化中断处理
  trap_init();

  // 初始化批处理系统
  batch_init();

  // 启用中断
  sti();

  cprintf("\nKernel initialization completed!\n");
  cprintf("Starting batch processing system...\n");

  // 运行批处理系统
  batch_run();

  cprintf("\nAll programs completed. System halting...\n");

  // 系统完成，进入无限循环
  for (;;)
  {
    // 可以在这里添加简单的shell或其他交互功能
    uartpoll();
  }
}
