#ifndef _MMU_H_
#define _MMU_H_

// 内存管理单元定义

// 段选择子
#define SEG_KCODE 1 // 内核代码段
#define SEG_KDATA 2 // 内核数据段
#define SEG_UCODE 3 // 用户代码段
#define SEG_UDATA 4 // 用户数据段
#define SEG_TSS 5   // 任务状态段

// 段描述符类型
#define STA_X 0x8 // 可执行段
#define STA_W 0x2 // 可写段
#define STA_R 0x2 // 可读段

// CR0寄存器标志位
#define CR0_PE 0x00000001 // 保护模式启用
#define CR0_WP 0x00010000 // 写保护
#define CR0_PG 0x80000000 // 分页启用

// 页目录和页表常量
#define NPDENTRIES 1024 // 每个页目录的条目数
#define NPTENTRIES 1024 // 每个页表的条目数
#define PGSIZE 4096     // 页大小

// 页表/目录项标志位
#define PTE_P 0x001  // 存在
#define PTE_W 0x002  // 可写
#define PTE_U 0x004  // 用户可访问
#define PTE_PS 0x080 // 页大小

// 地址转换宏
#define PGROUNDUP(sz) (((sz) + PGSIZE - 1) & ~(PGSIZE - 1))
#define PGROUNDDOWN(a) (((a)) & ~(PGSIZE - 1))

// 内核栈大小
#define KSTACKSIZE 4096

#ifndef __ASSEMBLER__
// 页表项
typedef uint pte_t;

// 页目录项
typedef uint pde_t;

// 段描述符结构
struct segdesc
{
    uint lim_15_0 : 16;  // 段限长低16位
    uint base_15_0 : 16; // 段基址低16位
    uint base_23_16 : 8; // 段基址中8位
    uint type : 4;       // 段类型
    uint s : 1;          // 0 = 系统, 1 = 应用
    uint dpl : 2;        // 描述符特权级
    uint p : 1;          // 存在位
    uint lim_19_16 : 4;  // 段限长高4位
    uint avl : 1;        // 可用位
    uint rsv1 : 1;       // 保留位
    uint db : 1;         // 0 = 16位段, 1 = 32位段
    uint g : 1;          // 粒度: 0 = 字节, 1 = 4KB
    uint base_31_24 : 8; // 段基址高8位
};

// 特权级定义
#define DPL_USER 0x3   // 用户特权级
#define DPL_KERNEL 0x0 // 内核特权级

// TSS 结构
struct tss
{
    uint link;                                   // 旧的TSS选择子
    uint esp0;                                   // 内核栈指针
    uint ss0;                                    // 内核栈段选择子
    uint esp1;                                   // 未使用
    uint ss1;                                    // 未使用
    uint esp2;                                   // 未使用
    uint ss2;                                    // 未使用
    uint cr3;                                    // 页目录基址
    uint eip;                                    // 指令指针
    uint eflags;                                 // 标志寄存器
    uint eax, ecx, edx, ebx, esp, ebp, esi, edi; // 通用寄存器
    uint es, cs, ss, ds, fs, gs;                 // 段寄存器
    uint ldt;                                    // LDT选择子
    uint t;                                      // 陷阱标志
    uint iomb;                                   // I/O映射基址
};
#endif

#endif // _MMU_H_
